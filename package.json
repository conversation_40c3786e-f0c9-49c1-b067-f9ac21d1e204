{"name": "cleanspotmobile", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "build:web": "expo build:web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "~2.1.2", "@react-native-community/netinfo": "11.4.1", "expo": "53.0.11", "expo-constants": "~17.1.6", "expo-firebase-core": "^3.1.0", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-router": "~5.0.7", "expo-status-bar": "~2.2.3", "firebase": "^10.12.2", "lodash.debounce": "^4.0.8", "metro-minify-terser": "^0.82.4", "react": "19.0.0", "react-error-boundary": "^6.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "^1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "jest": "^29.7.0", "jest-expo": "^53.0.7"}, "private": true}